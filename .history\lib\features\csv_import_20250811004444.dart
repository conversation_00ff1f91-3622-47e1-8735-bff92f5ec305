import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'dart:io';

class CsvImportButton extends ConsumerWidget {
  const CsvImportButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ElevatedButton.icon(
      onPressed: () => _importCsv(context, ref),
      icon: const Icon(Icons.upload_file),
      label: const Text('Importer CSV'),
    );
  }

  Future<void> _importCsv(BuildContext context, WidgetRef ref) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['csv'],
    );

    if (result?.files.single.path != null) {
      try {
        final file = File(result!.files.single.path!);
        final csvData = await file.readAsString();
        final rows = const CsvToListConverter().convert(csvData);
        
        // TODO: Parser et mettre à jour les providers
        // Format attendu: Project, Task, Budget, AC, %Done, Due
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('CSV importé avec succès')),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur import CSV: $e')),
          );
        }
      }
    }
  }
}