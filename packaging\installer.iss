; Inno Setup script example for packaging the Flutter Windows build
#define MyAppName "PMO & EA Dashboard"
#define MyAppVersion "0.2.0"
#define MyAppPublisher "HCP-DESIGN"
#define MyAppExeName "pmo_ea_dashboard.exe"

[Setup]
AppId={{8E1E5B82-9F7F-4F25-9B73-0A2D9E1C1C22}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
OutputDir=.
OutputBaseFilename=Setup_PMO_EA_Dashboard_v2
Compression=lzma
SolidCompression=yes

[Files]
Source: "..\build\windows\x64\runner\Release\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\build\windows\x64\runner\Release\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{commondesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Tasks]
Name: "desktopicon"; Description: "Créer un raccourci sur le Bureau"; GroupDescription: "Raccourcis:"; Flags: unchecked