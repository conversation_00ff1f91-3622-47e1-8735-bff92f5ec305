import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'asana_client.dart';

// ---- <PERSON><PERSON>PIs mock ----
class PmoKpis {
  final int progressPercent;
  final int onTrack;
  final int atRisk;
  final int offTrack;
  final int deadlineHitRate;
  final int budgetUsed;
  final double spi;
  final double cpi;

  PmoKpis({
    required this.progressPercent,
    required this.onTrack,
    required this.atRisk,
    required this.offTrack,
    required this.deadlineHitRate,
    required this.budgetUsed,
    required this.spi,
    required this.cpi,
  });
}

final pmoKpisProvider = Provider<PmoKpis>((ref) {
  return PmoKpis(
    progressPercent: 62,
    onTrack: 4,
    atRisk: 2,
    offTrack: 1,
    deadlineHitRate: 78,
    budgetUsed: 55,
    spi: 0.94,
    cpi: 1.05,
  );
});

final pmoTrendProvider = Provider<List<double>>((ref) {
  return [3, 4.2, 3.8, 5.1, 6, 5.6, 7.2, 7.8, 8.1, 7.5, 8.7, 9.1];
});

// ---- Risks mock ----
class RiskItem {
  final String title;
  final String impact;
  final String probability;
  final String owner;
  final String level;
  RiskItem(this.title, this.impact, this.probability, this.owner, this.level);
}

final risksProvider = Provider<List<RiskItem>>((ref) {
  return [
    RiskItem('Dépendance fournisseur critique', 'Élevé', 'Moyen', 'Nadia', 'Critique'),
    RiskItem('Retard lot 2', 'Moyen', 'Élevé', 'Serge', 'Élevé'),
    RiskItem('Sous-capacité QA', 'Moyen', 'Moyen', 'Aline', 'Moyen'),
    RiskItem('Budget formation insuffisant', 'Faible', 'Moyen', 'Guy', 'Moyen'),
    RiskItem('Churn SME', 'Élevé', 'Faible', 'Yao', 'Élevé'),
  ];
});

// ---- EA KPIs mock ----
class EaKpis {
  final int topPriorities;
  final int conflicts;
  final int decisionsPending;
  final int docsToSign;
  final int urgentTodos;
  final int meetingPrepRate;
  EaKpis({
    required this.topPriorities,
    required this.conflicts,
    required this.decisionsPending,
    required this.docsToSign,
    required this.urgentTodos,
    required this.meetingPrepRate,
  });
}

final eaKpisProvider = Provider<EaKpis>((ref) {
  return EaKpis(
    topPriorities: 3,
    conflicts: 1,
    decisionsPending: 5,
    docsToSign: 2,
    urgentTodos: 4,
    meetingPrepRate: 67,
  );
});

class Meeting {
  final String subject;
  final String start;
  final String location;
  final bool briefReady;
  Meeting(this.subject, this.start, this.location, this.briefReady);
}

final meetingsProvider = Provider<List<Meeting>>((ref) {
  return [
    Meeting('Revue OKR Q3', 'Lun 9:00-10:00', 'Salle Atlas', true),
    Meeting('Point DG — Partenariats', 'Lun 11:30-12:00', 'Bureau DG', false),
    Meeting('Comité projet Croissance+', 'Mar 14:00-15:30', 'Salle Delta', true),
    Meeting('Entretien candidat PMO', 'Mer 10:00-10:45', 'Salle Bravo', false),
    Meeting('Revue budget Focus RH', 'Jeu 16:00-17:00', 'Teams', true),
    Meeting('Coordination JIF 2025', 'Ven 09:00-10:00', 'Teams', true),
  ];
});

class InboxItem {
  final String title;
  final String from;
  final String receivedAt;
  final String category;
  InboxItem(this.title, this.from, this.receivedAt, this.category);
}

final inboxProvider = Provider<List<InboxItem>>((ref) {
  return [
    InboxItem('Validation devis impression', 'AGL', 'Hier 16:42', 'Validation'),
    InboxItem('Demande ordre de mission', 'Salma', 'Aujourd’hui 08:15', 'Admin'),
    InboxItem('Brief conférence Kigali', 'PMI CI', 'Aujourd’hui 10:03', 'Brief'),
    InboxItem('Proposition partenariat', 'BNI', 'Aujourd’hui 10:25', 'Partenariat'),
    InboxItem('Suivi factures', 'Finance', 'Aujourd’hui 11:00', 'Finance'),
    InboxItem('CR réunion RH', 'Focus RH', 'Hier 18:20', 'Compte rendu'),
  ];
});

// ---- Overview KPIs mock ----
class OverviewKpis {
  final int progress;
  final int criticalRisks;
  final int decisionsDone;
  final int decisionsPending;
  final int stakeholderEngagement;
  OverviewKpis(this.progress, this.criticalRisks, this.decisionsDone, this.decisionsPending, this.stakeholderEngagement);
}

final overviewKpisProvider = Provider<OverviewKpis>((ref) {
  return OverviewKpis(62, 3, 7, 5, 74);
});

class Blocker {
  final String title;
  final String project;
  final String owner;
  final int urgencyDays;
  Blocker(this.title, this.project, this.owner, this.urgencyDays);
}

final blockersProvider = Provider<List<Blocker>>((ref) {
  return [
    Blocker('Arbitrage scope Lot 3', 'Croissance+', 'DG', 2),
    Blocker('Validation budget évènement', 'JIF 2025', 'Finance', 3),
    Blocker('Décision choix prestataire', 'WIC Académie', 'Comité', 1),
    Blocker('Prolongation ressource externe', 'PMI Event', 'RH', 5),
    Blocker('Approbation contrat imprimeur', 'HCP Design', 'Legal', 4),
  ];
});

// ---- Projects mock (si pas de token Asana) ----
final projectsProvider = Provider<List<AsanaProject>>((ref) {
  return [
    AsanaProject('P1', 'Croissance+'),
    AsanaProject('P2', 'WIC Académie'),
    AsanaProject('P3', 'JIF 2025'),
  ];
});