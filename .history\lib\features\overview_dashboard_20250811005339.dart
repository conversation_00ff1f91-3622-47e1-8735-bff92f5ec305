import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/kpi_card.dart';
import '../widgets/section_title.dart';
import '../widgets/sales_chart.dart';
import '../data/sample_data.dart';
import '../data/export_pdf.dart';
import '../data/export_excel.dart';

class OverviewDashboard extends ConsumerWidget {
  const OverviewDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ov = ref.watch(overviewKpisProvider);
    final trend = ref.watch(pmoTrendProvider);

    Future<void> exportPdf() async {
      final kpis = {
        'Thermomètre avancement': '${ov.progress}%',
        'Risques critiques': ov.criticalRisks,
        'Décisions prises (sem.)': ov.decisionsDone,
        'Décisions en attente': ov.decisionsPending,
        'Engagement parties prenantes': '${ov.stakeholderEngagement}%',
      };
      final path = await exportKpisToPdf(kpis);
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('PDF enregistré: $path')));
      }
    }

    Future<void> exportExcel() async {
      final kpis = {
        'Thermomètre avancement': '${ov.progress}%',
        'Risques critiques': ov.criticalRisks,
        'Décisions prises (sem.)': ov.decisionsDone,
        'Décisions en attente': ov.decisionsPending,
        'Engagement parties prenantes': '${ov.stakeholderEngagement}%',
      };
      final path = await exportKpisToExcel(kpis: kpis);
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('Excel enregistré: $path')));
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                  onPressed: exportPdf,
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('Exporter PDF')),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                  onPressed: exportExcel,
                  icon: const Icon(Icons.grid_on),
                  label: const Text('Exporter Excel')),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              KpiCard(
                  title: 'Thermomètre avancement', value: '${ov.progress}%'),
              KpiCard(
                  title: 'Top 5 risques',
                  value: '${ov.criticalRisks} critiques'),
              KpiCard(
                  title: 'Décisions prises (semaine)',
                  value: '${ov.decisionsDone}'),
              KpiCard(
                  title: 'Décisions en attente',
                  value: '${ov.decisionsPending}'),
              KpiCard(
                  title: 'Engagement parties prenantes',
                  value: '${ov.stakeholderEngagement}%'),
            ],
          ),
          const SizedBox(height: 24),
          const SectionTitle('Tendance de réalisation (Top projets)'),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(height: 260, child: SalesChart(data: trend)),
            ),
          ),
          const SizedBox(height: 24),
          const SectionTitle('Points bloquants & arbitrages demandés'),
          Card(
            child: Column(
              children: [
                for (final b in ref.watch(blockersProvider).take(5))
                  ListTile(
                    leading: const Icon(Icons.block),
                    title: Text(b.title),
                    subtitle:
                        Text('Projet: ${b.project} | Responsable: ${b.owner}'),
                    trailing: Chip(label: Text('J-${b.urgencyDays}')),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
