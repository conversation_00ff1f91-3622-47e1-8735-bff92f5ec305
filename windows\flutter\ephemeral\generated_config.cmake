# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2" PROJECT_DIR)

set(FLUTTER_VERSION "0.2.0+2" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 2 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "PROJECT_DIR=J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2"
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2"
  "FLUTTER_TARGET=J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=J:\\pmo_ea_dashboard_v2\\pmo_ea_dashboard_v2\\.dart_tool\\package_config.json"
)
