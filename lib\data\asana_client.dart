import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AsanaProject { final String gid; final String name; AsanaProject(this.gid, this.name);
  factory AsanaProject.fromJson(Map j)=>AsanaProject(j['gid'], j['name']);
}

class AsanaTask {
  final String gid, name, dueOn, assigneeName, completed;
  final double? budget; final double? actualCost; final double percentDone;
  AsanaTask({required this.gid, required this.name, required this.dueOn, required this.assigneeName,
    required this.completed, this.budget, this.actualCost, required this.percentDone});
  factory AsanaTask.fromJson(Map j){
    final cf = (j['custom_fields']??[]) as List;
    double? budget, ac, pct;
    for(final f in cf){
      if(f['name']=='Budget'){ budget = (f['number_value']??0).toDouble(); }
      if(f['name']=='AC'){ ac = (f['number_value']??0).toDouble(); }
      if(f['name']=='% Done'){ pct = (f['number_value']??0).toDouble(); }
    }
    return AsanaTask(
      gid: j['gid'], name: j['name']??'',
      dueOn: j['due_on']??'',
      assigneeName: j['assignee']?['name']??'—',
      completed: (j['completed']==true)?'Done':'Open',
      budget: budget, actualCost: ac, percentDone: pct ?? (j['completed']==true?100:0),
    );
  }
}

class AsanaClient {
  final _base = Uri.parse('https://app.asana.com/api/1.0');
  String? get _token => dotenv.env['ASANA_TOKEN'];
  Map<String,String> get _h => {'Authorization':'Bearer ${_token!}'};

  Future<List<AsanaProject>> listProjects() async {
    if(_token==null || _token!.isEmpty) return [];
    final r = await http.get(_base.replace(path: '/api/1.0/projects'), headers: _h);
    final data = (json.decode(r.body)['data'] as List?)??[];
    return data.map((e)=>AsanaProject.fromJson(e)).toList();
  }

  Future<List<AsanaTask>> listTasks(String projectGid) async {
    if(_token==null || _token!.isEmpty) return [];
    final r = await http.get(_base.replace(path: '/api/1.0/tasks', queryParameters: {
      'project': projectGid,
      'opt_fields': 'name,due_on,completed,assignee.name,custom_fields.name,custom_fields.number_value'
    }), headers: _h);
    final data = (json.decode(r.body)['data'] as List?)??[];
    return data.map((e)=>AsanaTask.fromJson(e)).toList();
  }
}