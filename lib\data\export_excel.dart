import 'dart:io';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';

Future<String> exportKpisToExcel({
  required Map<String, dynamic> kpis,
  String fileName = 'Rapport_DG.xlsx',
}) async {
  final excel = Excel.createExcel();
  final sheet = excel['Hebdo'];
  sheet.appendRow([TextCellValue('KPI'), TextCellValue('Valeur')]);
  kpis.forEach((k,v)=> sheet.appendRow([TextCellValue(k), TextCellValue(v.toString())]));

  final bytes = excel.encode()!;
  Directory? dir = await getDownloadsDirectory();
  dir ??= await getApplicationDocumentsDirectory();
  final path = '${dir.path}/$fileName';
  File(path).createSync(recursive: true);
  File(path).writeAsBytesSync(bytes);
  return path;
}