# PMO & Executive Assistant Dashboard — v2 (Flutter Windows)

**Nouveautés v2 :**
- Connecteurs **<PERSON><PERSON>** (projets/tâches) & **Outlook** (Microsoft Graph — agenda) *MVP*.
- **Import CSV** local (projets, tâches).
- **Sélecteur période** (jour/semaine/mois) + **filtre projet**.
- **EVM** (SPI/CPI/EAC) à partir de Budget/AC/%Done.
- **Export PDF / Excel** (rapport hebdomadaire DG).

## Démarrage
1) Installez les prérequis (Flutter stable, VS 2022 C++ Desktop).
2) Placez un fichier `.env` à la racine (voir `.env.example`).
3) Dans ce dossier :
```
flutter create .
flutter pub get
flutter run -d windows
```
4) Build Windows :
```
flutter build windows
```

## .env
Copiez `.env.example` → `.env` et remplissez :
```
ASANA_TOKEN=pat_xxx
MS_GRAPH_TOKEN=eyJ...   # temporaire pour tester
```

## Packaging
- **MSIX** recommandé : ajoutez `msix` + `msix_config` puis `dart run msix:create`
- **Inno Setup** : utilisez `packaging/installer.iss` après `flutter build windows`

> Remarque : Sans tokens, l'app utilise des **données factices** (providers).