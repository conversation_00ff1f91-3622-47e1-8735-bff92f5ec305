import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'features/pmo_dashboard.dart';
import 'features/ea_dashboard.dart';
import 'features/overview_dashboard.dart';
import 'widgets/sidebar.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    // .env file is optional, continue without it
    print('Warning: .env file not found, continuing without environment variables');
  }
  runApp(const ProviderScope(child: App()));
}

class App extends StatefulWidget {
  const App({super.key});
  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  int index = 0;

  @override
  Widget build(BuildContext context) {
    final pages = const [PmoDashboard(), EaDashboard(), OverviewDashboard()];
    final titles = const ['PMO', 'Assist. Exécutif', 'Vue 360'];

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Dashboard PMO & EA',
      theme: ThemeData(
        colorSchemeSeed: Colors.indigo,
        useMaterial3: true,
      ),
      home: Scaffold(
        body: Row(
          children: [
            const Sidebar(),
            Expanded(
              child: Container(
                color: const Color(0xFFF7FAFC),
                child: pages[index],
              ),
            ),
          ],
        ),
      ),
    );
  }
}