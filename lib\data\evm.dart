class EvmInput {
  final double budgetAtCompletion; // BAC
  final List<double> taskBudgets;  // budgets par tâche
  final List<double> taskActuals;  // coûts réels par tâche
  final List<double> taskPercentDone; // 0..100
  EvmInput({required this.budgetAtCompletion, required this.taskBudgets, required this.taskActuals, required this.taskPercentDone});
}

class EvmOut { final double ev,pv,ac,spi,cpi,eac,vac;
  EvmOut(this.ev,this.pv,this.ac,this.spi,this.cpi,this.eac,this.vac);
}

EvmOut computeEvm(EvmInput i, {required double plannedPercentToDate}) {
  final ev = _dot(i.taskBudgets, i.taskPercentDone.map((p)=>p/100).toList());
  final pv = i.budgetAtCompletion * plannedPercentToDate;
  final ac = i.taskActuals.fold(0.0, (a,b)=>a+b);
  final spi = pv==0? 1.0 : ev/pv;
  final cpi = ac==0? 1.0 : ev/ac;
  final eac = cpi==0? i.budgetAtCompletion : i.budgetAtCompletion / cpi;
  final vac = i.budgetAtCompletion - eac;
  return EvmOut(ev,pv,ac,spi,cpi,eac,vac);
}

double _dot(List<double>a,List<double>b){ double s=0; for(int k=0;k<a.length && k<b.length;k++){ s+=a[k]*b[k]; } return s; }