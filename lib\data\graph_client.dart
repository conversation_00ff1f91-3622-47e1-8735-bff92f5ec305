import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class GraphEvent { final String subject, start, location; final bool briefReady;
  GraphEvent(this.subject,this.start,this.location,this.briefReady);
}

class GraphClient {
  String? get _token => dotenv.env['MS_GRAPH_TOKEN'];
  Map<String,String> get _h => {'Authorization':'Bearer ${_token!}','Content-Type':'application/json'};

  Future<List<GraphEvent>> next7Days() async {
    if(_token==null || _token!.isEmpty) return [];
    final now = DateTime.now().toUtc().toIso8601String();
    final to = DateTime.now().toUtc().add(const Duration(days: 7)).toIso8601String();
    final uri = Uri.parse('https://graph.microsoft.com/v1.0/me/calendarView?startDateTime=$now&endDateTime=$to&%24top=25');
    final r = await http.get(uri, headers: _h);
    if(r.statusCode!=200) return [];
    final items = (json.decode(r.body)['value'] as List?)??[];
    return items.map((e)=>GraphEvent(
      e['subject']??'',
      e['start']?['dateTime']??'',
      e['location']?['displayName']??'',
      false,
    )).toList();
  }
}