﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A95B6728-1C77-30F8-B715-C15667B3B9F3}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>printing_plugin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">printing_plugin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">printing_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">printing_plugin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">printing_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">printing_plugin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">printing_plugin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Debug";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\pdfium-src\lib\pdfium.dll.lib;..\..\flutter\Debug\flutter_wrapper_plugin.lib;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Debug/printing_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Debug/printing_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Profile";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\pdfium-src\lib\pdfium.dll.lib;..\..\flutter\Profile\flutter_wrapper_plugin.lib;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Profile/printing_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Profile/printing_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR="Release";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_PLUGIN_IMPL;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\";printing_plugin_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\cpp_client_wrapper\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\include\cpp;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\pdfium-src\lib\pdfium.dll.lib;..\..\flutter\Release\flutter_wrapper_plugin.lib;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Release/printing_plugin.lib</ImportLibrary>
      <ProgramDataBaseFile>J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/Release/printing_plugin.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\CMakeLists.txt">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows/flutter/ephemeral/.plugin_symlinks/printing/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64 --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeParseArguments.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageMessage.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\PDFiumConfig.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.CMakeLists.cmake.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows/flutter/ephemeral/.plugin_symlinks/printing/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64 --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeParseArguments.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageMessage.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\PDFiumConfig.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.CMakeLists.cmake.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows/flutter/ephemeral/.plugin_symlinks/printing/windows/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/windows -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64 --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/plugins/printing/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeParseArguments.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\FindPackageMessage.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-src\PDFiumConfig.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.CMakeLists.cmake.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\DownloadProject.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\plugins\printing\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\printing.cpp" />
    <ClInclude Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\printing.h" />
    <ClCompile Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\printing_plugin.cpp" />
    <ClInclude Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\include\printing\printing_plugin.h" />
    <ClCompile Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\print_job.cpp" />
    <ClInclude Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\windows\flutter\ephemeral\.plugin_symlinks\printing\windows\print_job.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{1928A516-6D87-30A8-B99E-1D964FDE979E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{E897D4F4-1F19-30EA-B944-74233A66975E}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\flutter\flutter_wrapper_plugin.vcxproj">
      <Project>{BFBAD519-449A-3A0E-A58C-86E398143556}</Project>
      <Name>flutter_wrapper_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>