The system is: Windows - 10.0.26120 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler:  
Build flags: 
Id flags:  

The output was:
0
Microsoft (R) Build Engine version 16.11.6+a918ceb31 pour .NET Framework
Copyright (C) Microsoft Corporation. Tous droits réservés.

La génération a démarré 11/08/2025 01:14:18.
Projet "J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CompilerIdCXX\CompilerIdCXX.vcxproj" sur le noud 1 (cibles par défaut).
PrepareForBuild:
  Création du répertoire "Debug\".
  Création du répertoire "Debug\CompilerIdCXX.tlog\".
InitializeBuildStatus:
  Création de "Debug\CompilerIdCXX.tlog\unsuccessfulbuild", car "AlwaysCreate" a été spécifié.
ClCompile:
  C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\" /Fd"Debug\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
  CMakeCXXCompilerId.cpp
Link:
  C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:".\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\CompilerIdCXX.lib" /MACHINE:X64 Debug\CMakeCXXCompilerId.obj
  CompilerIdCXX.vcxproj -> J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CompilerIdCXX\CompilerIdCXX.exe
PostBuildEvent:
  for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
  :VCEnd
  CMAKE_CXX_COMPILER=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cl.exe
FinalizeBuildStatus:
  Suppression du fichier "Debug\CompilerIdCXX.tlog\unsuccessfulbuild".
  Mise à jour de l'horodatage "Debug\CompilerIdCXX.tlog\CompilerIdCXX.lastbuildstate".
Génération du projet "J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\CMakeFiles\3.20.21032501-MSVC_2\CompilerIdCXX\CompilerIdCXX.vcxproj" terminée (cibles par défaut).

La génération a réussi.
    0 Avertissement(s)
    0 Erreur(s)

Temps écoulé 00:00:01.10


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"

The CXX compiler identification is MSVC, found in "J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/CMakeFiles/3.20.21032501-MSVC_2/CompilerIdCXX/CompilerIdCXX.exe"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe cmTC_f47ef.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:m && Microsoft (R) Build Engine version 16.11.6+a918ceb31 pour .NET Framework

Copyright (C) Microsoft Corporation. Tous droits réservés.



  Compilateur d'optimisation Microsoft (R) C/C++ version 19.29.30159 pour x64

  CMakeCXXCompilerABI.cpp

  Copyright (C) Microsoft Corporation. Tous droits réservés.

  cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\"Debug\"" /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f47ef.dir\Debug\\" /Fd"cmTC_f47ef.dir\Debug\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeCXXCompilerABI.cpp"

  cmTC_f47ef.vcxproj -> J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\CMakeFiles\CMakeTmp\Debug\cmTC_f47ef.exe




