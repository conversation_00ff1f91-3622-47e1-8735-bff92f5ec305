class EvmResult {
  final double bac;
  final double ev;
  final double pv;
  final double ac;
  final double spi;
  final double cpi;
  final double eac;
  final double vac;
  
  EvmResult({
    required this.bac,
    required this.ev,
    required this.pv,
    required this.ac,
    required this.spi,
    required this.cpi,
    required this.eac,
    required this.vac,
  });
}

EvmResult calculateEvm({
  required List<double> taskBudgets,
  required List<double> taskActuals,
  required List<int> taskPercentDone,
  required double plannedPercentToDate,
}) {
  final bac = taskBudgets.fold(0.0, (sum, budget) => sum + budget);
  
  final ev = taskBudgets.asMap().entries.fold(0.0, (sum, entry) {
    final index = entry.key;
    final budget = entry.value;
    final percentDone = taskPercentDone[index];
    return sum + (budget * percentDone / 100);
  });
  
  final pv = bac * plannedPercentToDate;
  final ac = taskActuals.fold(0.0, (sum, actual) => sum + actual);
  
  final spi = pv > 0 ? ev / pv : 0.0;
  final cpi = ac > 0 ? ev / ac : 0.0;
  final eac = cpi > 0 ? bac / cpi : bac;
  final vac = bac - eac;
  
  return EvmResult(
    bac: bac,
    ev: ev,
    pv: pv,
    ac: ac,
    spi: spi,
    cpi: cpi,
    eac: eac,
    vac: vac,
  );
}