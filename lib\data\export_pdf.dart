import 'dart:io';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';

Future<String> exportKpisToPdf(Map<String, dynamic> kpis, {String fileName='Rapport_DG.pdf'}) async {
  final doc = pw.Document();
  doc.addPage(pw.Page(
    build: (ctx) => pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text('Rapport hebdo DG', style: pw.TextStyle(fontSize: 22)),
        pw.SizedBox(height: 12),
        for(final e in kpis.entries)
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [pw.Text(e.key), pw.Text(e.value.toString())],
          ),
      ],
    ),
  ));
  Directory? dir = await getDownloadsDirectory();
  dir ??= await getApplicationDocumentsDirectory();
  final path = '${dir.path}/$fileName';
  File(path).createSync(recursive: true);
  File(path).writeAsBytesSync(await doc.save());
  return path;
}