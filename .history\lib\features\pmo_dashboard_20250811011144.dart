import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/kpi_card.dart';
import '../widgets/section_title.dart';
import '../widgets/sales_chart.dart';
import '../widgets/circular_chart.dart';
import '../data/sample_data.dart';
import '../data/filters.dart';
import '../data/evm.dart';

class PmoDashboard extends ConsumerWidget {
  const PmoDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final kpis = ref.watch(pmoKpisProvider);
    final trend = ref.watch(pmoTrendProvider);
    final period = ref.watch(periodProvider);
    final selectedProject = ref.watch(selectedProjectProvider);

    // EVM exemple (mock)
    final evm = computeEvm(
      EvmInput(
        budgetAtCompletion: 1000000,
        taskBudgets: [200000, 300000, 500000],
        taskActuals: [180000, 350000, 200000],
        taskPercentDone: [100, 60, 25],
      ),
      plannedPercentToDate: 0.58,
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          // En-tête du dashboard
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Dashboard',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Overview of your Month',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    // Sélecteur de période moderne
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7FAFC),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: Period.values.map((p) {
                          final isSelected = period == p;
                          final label = switch(p) {
                            Period.day => 'DAILY',
                            Period.week => 'WEEKLY', 
                            Period.month => 'MONTHLY'
                          };
                          return GestureDetector(
                            onTap: () => ref.read(periodProvider.notifier).state = p,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.white : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ] : null,
                              ),
                              child: Text(
                                label,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected ? const Color(0xFF2D3748) : const Color(0xFF718096),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Date range
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE2E8F0)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16, color: Color(0xFF718096)),
                          const SizedBox(width: 8),
                          Text(
                            '10-06-2020    to    10-16-2020',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16, runSpacing: 16,
            children: [
              KpiCard(
                title: 'Tâches Complétées',
                value: '${kpis.progressPercent}+',
                subtitle: 'Tâches closes',
                icon: Icons.task_alt,
                gradientColors: const [Color(0xFF6C5CE7), Color(0xFF8B7ED8)],
              ),
              KpiCard(
                title: 'Projets Actifs',
                value: '${kpis.onTrack}+',
                subtitle: 'En cours',
                icon: Icons.trending_up,
                gradientColors: const [Color(0xFF4285F4), Color(0xFF5A9BF8)],
              ),
              KpiCard(
                title: 'Échéances Respectées',
                value: '${kpis.deadlineHitRate}+',
                subtitle: 'Dans les temps',
                icon: Icons.schedule,
                gradientColors: const [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
              ),
              KpiCard(
                title: 'Budget Utilisé',
                value: '${kpis.budgetUsed}+',
                subtitle: 'Consommé',
                icon: Icons.account_balance_wallet,
                gradientColors: const [Color(0xFFFF9500), Color(0xFFFFAB40)],
              ),
              KpiCard(
                title: 'SPI Performance',
                value: evm.spi.toStringAsFixed(2),
                subtitle: 'Schedule Performance',
                icon: Icons.speed,
                gradientColors: evm.spi >= 1 
                  ? const [Color(0xFF4CAF50), Color(0xFF66BB6A)]
                  : const [Color(0xFFF44336), Color(0xFFEF5350)],
              ),
              KpiCard(
                title: 'CPI Performance',
                value: evm.cpi.toStringAsFixed(2),
                subtitle: 'Cost Performance',
                icon: Icons.monetization_on,
                gradientColors: evm.cpi >= 1 
                  ? const [Color(0xFF4CAF50), Color(0xFF66BB6A)]
                  : const [Color(0xFFF44336), Color(0xFFEF5350)],
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Graphique linéaire
              Expanded(
                flex: 2,
                child: SizedBox(height: 400, child: SalesChart(data: trend)),
              ),
              const SizedBox(width: 24),
              // Graphique circulaire et métriques
              Expanded(
                flex: 1,
                child: Container(
                  height: 400,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        const Text(
                          'Analytics',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D3748),
                          ),
                        ),
                        const SizedBox(height: 20),
                        CircularChart(
                          percentage: 80,
                          title: 'Performance',
                          subtitle: 'Overall',
                        ),
                        const SizedBox(height: 30),
                        // Activités récentes
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Recent Activities',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D3748),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: ListView(
                            children: [
                              _buildActivityItem(
                                '40 Mins Ago',
                                'Task Updated',
                                'Nicolas updated a task',
                                const Color(0xFFFF6B6B),
                              ),
                              _buildActivityItem(
                                '1 Day ago',
                                'Deal Added',
                                'Pamela updated a Task',
                                const Color(0xFF6C5CE7),
                              ),
                              _buildActivityItem(
                                '40 Mins Ago',
                                'Published Article',
                                'Sophia published an article',
                                const Color(0xFF4285F4),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Order Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D3748),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF7FAFC),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFFE2E8F0)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.search, size: 16, color: Color(0xFF718096)),
                            const SizedBox(width: 8),
                            const Text(
                              'Search',
                              style: TextStyle(
                                color: Color(0xFF718096),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // En-têtes du tableau
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF7FAFC),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Expanded(flex: 2, child: Text('INVOICE', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF4A5568), fontSize: 12))),
                        Expanded(flex: 2, child: Text('CUSTOMERS', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF4A5568), fontSize: 12))),
                        Expanded(flex: 2, child: Text('FROM', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF4A5568), fontSize: 12))),
                        Expanded(flex: 1, child: Text('PRICE', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF4A5568), fontSize: 12))),
                        Expanded(flex: 1, child: Text('STATUS', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF4A5568), fontSize: 12))),
                      ],
                    ),
                  ),
                  // Lignes du tableau
                  ...ref.watch(risksProvider).take(5).map((r) => Container(
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0), width: 1)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            r.title.length > 6 ? r.title.substring(0, 6) : r.title,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF2D3748),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            r.owner,
                            style: const TextStyle(color: Color(0xFF4A5568)),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Risque',
                            style: const TextStyle(color: Color(0xFF4A5568)),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            '€${(r.impact * 100).toInt()}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF2D3748),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: r.level == 'High' 
                                ? const Color(0xFFFF6B6B).withOpacity(0.1)
                                : r.level == 'Medium'
                                  ? const Color(0xFFFFB800).withOpacity(0.1)
                                  : const Color(0xFF6C5CE7).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              r.level == 'High' ? 'Pending' : r.level == 'Medium' ? 'Paid' : 'Done',
                              style: TextStyle(
                                color: r.level == 'High' 
                                  ? const Color(0xFFFF6B6B)
                                  : r.level == 'Medium'
                                    ? const Color(0xFFFFB800)
                                    : const Color(0xFF6C5CE7),
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}