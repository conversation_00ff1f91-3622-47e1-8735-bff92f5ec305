import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/kpi_card.dart';
import '../widgets/section_title.dart';
import '../data/sample_data.dart';
import '../data/filters.dart';

class EaDashboard extends ConsumerWidget {
  const EaDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ea = ref.watch(eaKpisProvider);
    final meetings = ref.watch(meetingsProvider);
    final period = ref.watch(periodProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          Row(
            children: [
              DropdownButton<Period>(
                value: period,
                onChanged: (p)=>ref.read(periodProvider.notifier).state = p!,
                items: Period.values.map((p)=>DropdownMenuItem(value:p, child: Text(switch(p){Period.day=>'Jour',Period.week=>'Semaine',Period.month=>'Mois'}))).toList(),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16, runSpacing: 16,
            children: [
              KpiCard(title: 'Priorités du jour', value: '${ea.topPriorities}', subtitle: 'Top 3 horodatées'),
              KpiCard(title: 'Conflits d’agenda', value: '${ea.conflicts}'),
              KpiCard(title: 'Décisions en attente', value: '${ea.decisionsPending}'),
              KpiCard(title: 'Docs à signer', value: '${ea.docsToSign}'),
              KpiCard(title: 'Urgences < 48h', value: '${ea.urgentTodos}'),
              KpiCard(title: 'Préparation réunions', value: '${ea.meetingPrepRate}%', subtitle: 'Briefs prêts / J+7'),
            ],
          ),
          const SizedBox(height: 24),
          const SectionTitle('Agenda (prochains 7 jours)'),
          Card(
            child: Column(
              children: [
                for (final m in meetings.take(6))
                  ListTile(
                    leading: const Icon(Icons.event),
                    title: Text(m.subject),
                    subtitle: Text('${m.start} — ${m.location} | Brief: ${m.briefReady ? 'OK' : 'À préparer'}'),
                    trailing: const Icon(Icons.chevron_right),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const SectionTitle('Pipeline “À traiter”'),
          Card(
            child: Column(
              children: [
                for (final item in ref.watch(inboxProvider).take(6))
                  ListTile(
                    leading: const Icon(Icons.inbox),
                    title: Text(item.title),
                    subtitle: Text('De: ${item.from} | Reçu: ${item.receivedAt}'),
                    trailing: Chip(label: Text(item.category)),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}