﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{04807EB8-CF93-371E-9C2B-C5A173A87424}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>pdfium-download</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-mkdir.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-build
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/tmp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Creating directories for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-build
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/tmp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Creating directories for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-build
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/tmp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Creating directories for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-build
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/tmp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (download, verify and extract) for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\pdfium-download-urlinfo.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Performing download step (download, verify and extract) for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\pdfium-download-urlinfo.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Performing download step (download, verify and extract) for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\pdfium-download-urlinfo.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Performing download step (download, verify and extract) for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/download-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/verify-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/extract-pdfium-download.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\pdfium-download-urlinfo.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No update step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No update step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No update step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No update step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No patch step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No patch step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No patch step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-configure.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No configure step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No configure step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No configure step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-build.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No build step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No build step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No build step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-install.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No install step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No install step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No install step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e9423ff94e2bf02be3a8312567ffa30d\pdfium-download-test.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">No test step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">No test step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">No test step for 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-build
if %errorlevel% neq 0 goto :cmEnd
J:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\162b562ac40012b8d54f5bdd16b55be4\pdfium-download-complete.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)/pdfium-download-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Completed 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)/pdfium-download-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Completed 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)/pdfium-download-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Completed 'pdfium-download'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/$(Configuration)/pdfium-download-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/pdfium-download-prefix/src/pdfium-download-stamp/$(Configuration)/pdfium-download-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-install;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-mkdir;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-download;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-update;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-patch;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-configure;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-build;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\src\pdfium-download-stamp\$(Configuration)\pdfium-download-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\e7c21b20ddc4e5379319f7f7ab069333\pdfium-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\$(Configuration)\pdfium-download-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeLists.txt">
      <StdOutEncoding>UTF-8</StdOutEncoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeDetermineSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystem.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject-download.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\RepositoryInfo.txt.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeDetermineSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystem.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject-download.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\RepositoryInfo.txt.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeDetermineSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystem.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject-download.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\RepositoryInfo.txt.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeDetermineSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeGenericSystem.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeInitializeConfigs.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystem.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject-download.cmake.in;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\ExternalProject.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\Windows.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\Platform\WindowsPaths.cmake;C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.20\Modules\RepositoryInfo.txt.in;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\3.20.21032501-MSVC_2\CMakeSystem.cmake;J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\pdfium-download-prefix\tmp\pdfium-download-cfgcmd.txt.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\CMakeFiles\pdfium-download">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="J:\pmo_ea_dashboard_v2\pmo_ea_dashboard_v2\build\windows\x64\pdfium-download\ZERO_CHECK.vcxproj">
      <Project>{CA45DA7E-2884-3D2B-82C9-C895526E242F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>