import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class Circular<PERSON>hart extends StatelessWidget {
  final double percentage;
  final String title;
  final String subtitle;
  
  const Circular<PERSON>hart({
    super.key,
    required this.percentage,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 200,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 160,
            height: 160,
            child: <PERSON><PERSON><PERSON>(
              PieChartData(
                startDegreeOffset: -90,
                sectionsSpace: 0,
                centerSpaceRadius: 50,
                sections: [
                  PieChartSectionData(
                    value: percentage,
                    color: const Color(0xFF6C5CE7),
                    radius: 20,
                    showTitle: false,
                  ),
                  PieChartSectionData(
                    value: 100 - percentage,
                    color: const Color(0xFFE2E8F0),
                    radius: 20,
                    showTitle: false,
                  ),
                ],
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${percentage.toInt()}%',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                ),
              ),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF718096),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}