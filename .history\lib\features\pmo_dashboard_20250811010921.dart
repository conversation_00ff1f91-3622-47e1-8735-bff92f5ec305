import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/kpi_card.dart';
import '../widgets/section_title.dart';
import '../widgets/sales_chart.dart';
import '../data/sample_data.dart';
import '../data/filters.dart';
import '../data/evm.dart';

class PmoDashboard extends ConsumerWidget {
  const PmoDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final kpis = ref.watch(pmoKpisProvider);
    final trend = ref.watch(pmoTrendProvider);
    final period = ref.watch(periodProvider);
    final selectedProject = ref.watch(selectedProjectProvider);

    // EVM exemple (mock)
    final evm = computeEvm(
      EvmInput(
        budgetAtCompletion: 1000000,
        taskBudgets: [200000, 300000, 500000],
        taskActuals: [180000, 350000, 200000],
        taskPercentDone: [100, 60, 25],
      ),
      plannedPercentToDate: 0.58,
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          Row(
            children: [
              DropdownButton<Period>(
                value: period,
                onChanged: (p)=>ref.read(periodProvider.notifier).state = p!,
                items: Period.values.map((p)=>DropdownMenuItem(value:p, child: Text(switch(p){Period.day=>'Jour',Period.week=>'Semaine',Period.month=>'Mois'}))).toList(),
              ),
              const SizedBox(width: 12),
              DropdownButton<String>(
                value: selectedProject,
                hint: const Text('Projet'),
                onChanged: (id)=>ref.read(selectedProjectProvider.notifier).state = id,
                items: ref.watch(projectsProvider).map((p)=>DropdownMenuItem(value:p.gid, child: Text(p.name))).toList(),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16, runSpacing: 16,
            children: [
              KpiCard(
                title: 'Tâches Complétées',
                value: '${kpis.progressPercent}+',
                subtitle: 'Tâches closes',
                icon: Icons.task_alt,
                gradientColors: const [Color(0xFF6C5CE7), Color(0xFF8B7ED8)],
              ),
              KpiCard(
                title: 'Projets Actifs',
                value: '${kpis.onTrack}+',
                subtitle: 'En cours',
                icon: Icons.trending_up,
                gradientColors: const [Color(0xFF4285F4), Color(0xFF5A9BF8)],
              ),
              KpiCard(
                title: 'Échéances Respectées',
                value: '${kpis.deadlineHitRate}+',
                subtitle: 'Dans les temps',
                icon: Icons.schedule,
                gradientColors: const [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
              ),
              KpiCard(
                title: 'Budget Utilisé',
                value: '${kpis.budgetUsed}+',
                subtitle: 'Consommé',
                icon: Icons.account_balance_wallet,
                gradientColors: const [Color(0xFFFF9500), Color(0xFFFFAB40)],
              ),
              KpiCard(
                title: 'SPI Performance',
                value: evm.spi.toStringAsFixed(2),
                subtitle: 'Schedule Performance',
                icon: Icons.speed,
                gradientColors: evm.spi >= 1 
                  ? const [Color(0xFF4CAF50), Color(0xFF66BB6A)]
                  : const [Color(0xFFF44336), Color(0xFFEF5350)],
              ),
              KpiCard(
                title: 'CPI Performance',
                value: evm.cpi.toStringAsFixed(2),
                subtitle: 'Cost Performance',
                icon: Icons.monetization_on,
                gradientColors: evm.cpi >= 1 
                  ? const [Color(0xFF4CAF50), Color(0xFF66BB6A)]
                  : const [Color(0xFFF44336), Color(0xFFEF5350)],
              ),
            ],
          ),
          const SizedBox(height: 24),
          const SectionTitle('Tendance de réalisation (12 sem.)'),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(height: 260, child: SalesChart(data: trend)),
            ),
          ),
          const SizedBox(height: 24),
          const SectionTitle('Alertes & Risques critiques'),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (final r in ref.watch(risksProvider).take(5))
                    ListTile(
                      leading: const Icon(Icons.warning_amber_rounded),
                      title: Text(r.title),
                      subtitle: Text('Impact: ${r.impact} | Prob.: ${r.probability} | Propriétaire: ${r.owner}'),
                      trailing: Chip(label: Text(r.level)),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}