import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum Period { day, week, month }

final periodProvider = StateProvider<Period>((ref) => Period.week);
final selectedProjectProvider = StateProvider<String?>((ref) => null);

class FiltersWidget extends ConsumerWidget {
  const FiltersWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final period = ref.watch(periodProvider);
    final selectedProject = ref.watch(selectedProjectProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Filtre période
            SegmentedButton<Period>(
              segments: const [
                ButtonSegment(value: Period.day, label: Text('Jour')),
                ButtonSegment(value: Period.week, label: Text('Semaine')),
                ButtonSegment(value: Period.month, label: Text('Mois')),
              ],
              selected: {period},
              onSelectionChanged: (Set<Period> selection) {
                ref.read(periodProvider.notifier).state = selection.first;
              },
            ),
            const SizedBox(width: 24),
            // Filtre projet
            DropdownButton<String?>(
              value: selectedProject,
              hint: const Text('Tous les projets'),
              items: [
                const DropdownMenuItem(value: null, child: Text('Tous')),
                // TODO: Charger depuis projectsProvider
                const DropdownMenuItem(value: 'P1', child: Text('Croissance+')),
                const DropdownMenuItem(value: 'P2', child: Text('WIC Académie')),
              ],
              onChanged: (value) {
                ref.read(selectedProjectProvider.notifier).state = value;
              },
            ),
          ],
        ),
      ),
    );
  }
}