^J:\PMO_EA_DASHBOARD_V2\PMO_EA_DASHBOARD_V2\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download -BJ:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download --check-stamp-file J:/pmo_ea_dashboard_v2/pmo_ea_dashboard_v2/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
