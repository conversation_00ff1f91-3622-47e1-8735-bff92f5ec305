import 'dart:io';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';

class CsvTask { final String project; final String name; final double budget; final double ac; final double pct; final String due;
  CsvTask(this.project,this.name,this.budget,this.ac,this.pct,this.due);
}

Future<List<CsvTask>> pickAndParseTasks() async {
  final r = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['csv']);
  if(r==null) return [];
  final f = File(r.files.single.path!);
  final rows = const CsvToListConverter(eol: '\n').convert(await f.readAsString());
  // Colonnes attendues: Project,Task,Budget,AC,Pct,Due
  return rows.skip(1).map((e)=>CsvTask(
    e[0].toString(), e[1].toString(),
    (e[2]??0).toDouble(), (e[3]??0).toDouble(), (e[4]??0).toDouble(),
    e[5].toString(),
  )).toList();
}